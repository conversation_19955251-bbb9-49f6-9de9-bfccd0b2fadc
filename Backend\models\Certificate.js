const mongoose = require('mongoose');

const certificateSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  course: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course',
    required: true
  },
  quiz: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Quiz',
    required: true
  },
  certificateId: {
    type: String,
    required: true,
    unique: true
  },
  issueDate: {
    type: Date,
    default: Date.now
  },
  certificateUrl: {
    type: String
  },
  verificationUrl: {
    type: String
  },
  // Quiz performance data
  score: {
    type: Number,
    required: true
  },
  percentage: {
    type: Number,
    required: true
  },
  passingScore: {
    type: Number,
    required: true
  },
  isValid: {
    type: Boolean,
    default: true
  },
  metadata: {
    studentName: { type: String },
    courseName: { type: String },
    instructorName: { type: String },
    completionDate: { type: String },
    quizTitle: { type: String },
    achievedScore: { type: String }
  }
}, { timestamps: true });

// Generate unique certificate ID before saving
certificateSchema.pre('save', async function(next) {
  if (!this.certificateId) {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    this.certificateId = `CERT-${timestamp}-${random}`.toUpperCase();
  }
  next();
});

// Index for efficient queries
certificateSchema.index({ user: 1, course: 1, quiz: 1 });
certificateSchema.index({ certificateId: 1 });

module.exports = mongoose.model('Certificate', certificateSchema);
