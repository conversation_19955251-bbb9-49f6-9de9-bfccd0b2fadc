import React, { useState, useEffect } from 'react';
import './QuizSubmission.css';
import { showWarningAlert, showErrorAlert } from '../utils/alertService';
const QuizSubmission = ({ quiz, onSubmit, existingAttempt = null }) => {
  const [answers, setAnswers] = useState({});
  const [submitted, setSubmitted] = useState(false);
  const [results, setResults] = useState(null);
  const [loading, setLoading] = useState(false);
  // Check if this quiz has existing attempts when the component mounts or quiz changes
  useEffect(() => {
    // Reset state when the component mounts or quiz changes
    setSubmitted(false);
    setResults(null);
    setAnswers({});
    // Only if there's an existing attempt for the current user, show the results
    if (existingAttempt) {
      setSubmitted(true);
      setResults(existingAttempt);
      // Reconstruct answers from the attempt data if available
      if (existingAttempt.answers && Array.isArray(existingAttempt.answers)) {
        const reconstructedAnswers = {};
        existingAttempt.answers.forEach(answer => {
          if (answer.questionId) {
            reconstructedAnswers[answer.questionId] = answer.selectedAnswer;
          }
        });
        setAnswers(reconstructedAnswers);
      }
    }
  }, [existingAttempt, quiz._id]);
  const handleAnswerSelect = (questionId, answerIndex, questionType = 'single') => {
    if (questionType === 'multiple') {
      // Handle multiple selection
      const currentAnswers = answers[questionId] || [];
      const optionStr = answerIndex.toString();

      let updatedAnswers;
      if (currentAnswers.includes(optionStr)) {
        // Remove if already selected
        updatedAnswers = currentAnswers.filter(ans => ans !== optionStr);
      } else {
        // Add if not selected
        updatedAnswers = [...currentAnswers, optionStr];
      }

      setAnswers({
        ...answers,
        [questionId]: updatedAnswers
      });
    } else {
      // Handle single selection - store as array for consistency with backend
      setAnswers({
        ...answers,
        [questionId]: [answerIndex.toString()]
      });
    }
  };
  const handleSubmit = async () => {
    // Check if all questions are answered
    const allAnswered = quiz.questions.every(q => {
      const answer = answers[q._id];
      // Both single and multiple answers are now stored as arrays
      return answer && Array.isArray(answer) && answer.length > 0;
    });

    if (!allAnswered) {
      showWarningAlert('Please answer all questions before submitting.');
      return;
    }
    try {
      setLoading(true);
      const result = await onSubmit(answers);
      setResults(result);
      setSubmitted(true);
    } catch (error) {
      console.error('Error submitting quiz:', error);
      showErrorAlert('Failed to submit quiz. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  const handleRetry = () => {
    // Only reset the local state, don't delete the attempt from the database
    setAnswers({});
    setSubmitted(false);
    setResults(null);
  };
  return (
    <div className="quiz-submission-container">
      {!submitted ? (
        <>
          <div className="quiz-header">
            <h2>{quiz.title}</h2>
            <p>{quiz.description}</p>
            <div className="quiz-meta">
              {/* <span>Time Limit: {quiz.timeLimit} minutes</span>
              <span>Passing Score: {quiz.passingScore}%</span> */}
            </div>
          </div>
          <div className="quiz-questions">
            {quiz.questions.map((question, qIndex) => {
              const isMultiple = question.questionType === 'multiple';
              const currentAnswers = answers[question._id] || [];

              return (
                <div key={question._id} className="quiz-question">
                  <h3>Question {qIndex + 1}: {question.question}</h3>
                  {isMultiple && (
                    <p className="question-hint">Select all correct answers (multiple selections allowed)</p>
                  )}
                  <div className="quiz-options">
                    {question.options.map((option, oIndex) => {
                      const isChecked = currentAnswers.includes(oIndex.toString());

                      return (
                        <label key={oIndex} className="quiz-option">
                          <input
                            type={isMultiple ? "checkbox" : "radio"}
                            name={`question-${question._id}`}
                            checked={isChecked}
                            onChange={() => handleAnswerSelect(question._id, oIndex, question.questionType)}
                          />
                          <span>{option}</span>
                        </label>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </div>
          <div className="quiz-actions">
            <button
              className="quiz-submit-btn"
              onClick={handleSubmit}
              disabled={loading}
            >
              {loading ? 'Submitting...' : 'Submit Quiz'}
            </button>
          </div>
        </>
      ) : (
        <div className="quiz-results">
          <div className="quiz-result-header">
            <h2>{results.passed ? 'Congratulations!' : 'Quiz Results'}</h2>
            <div className="quiz-score">
              <div className={`score-circle ${results.passed ? 'passed' : 'failed'}`}>
                {results.percentage}%
              </div>
              <p>You {results.passed ? 'passed' : 'did not pass'} the quiz.</p>
              <p>Score: {results.score}/{results.totalPoints}</p>
              <p>Passing Score Required: {quiz.passingScore || 60}%</p>
              {results.passed && (
                <div className="certificate-info">
                  <p className="certificate-text">🎉 You've earned a certificate!</p>
                  <button
                    className="download-certificate-btn"
                    onClick={() => window.open(`/api/certificates/download-quiz/${quiz.course}/${quiz._id}`, '_blank')}
                  >
                    Download Certificate
                  </button>
                  {results.certificateGenerated && (
                    <p className="certificate-note">Certificate has been automatically generated for you!</p>
                  )}
                </div>
              )}
            </div>
          </div>
          <div className="quiz-answers-review">
            <h3>Review Your Answers</h3>
            {quiz.questions.map((question, qIndex) => {
              // Find the corresponding answer from results if available
              const answerFromResults = results.answers &&
                results.answers.find(a => a.questionIndex === qIndex);

              // Handle both single and multiple answers - now all stored as arrays
              const userAnswers = answerFromResults ?
                (Array.isArray(answerFromResults.selectedAnswer) ? answerFromResults.selectedAnswer : [answerFromResults.selectedAnswer]) :
                (answers[question._id] || []);

              const isCorrect = answerFromResults ? answerFromResults.isCorrect : false;
              const partialScore = answerFromResults ? answerFromResults.partialScore : 0;
              const maxPoints = question.points || 1;

              return (
                <div key={question._id} className={`review-question ${isCorrect ? 'correct' : 'partial'}`}>
                  <h4>Question {qIndex + 1}: {question.question}</h4>
                  {question.questionType === 'multiple' && (
                    <div className="score-info">
                      <span className="partial-score">
                        Score: {(partialScore ?? 0).toFixed(1)}/{maxPoints} points
                        {partialScore > 0 && partialScore < maxPoints && ' (Partial Credit)'}
                      </span>
                    </div>
                  )}
                  <div className="review-options">
                    {question.options.map((option, oIndex) => {
                      const isUserSelected = userAnswers.includes(oIndex.toString());
                      const isCorrectAnswer = question.correctAnswers?.some(ca => ca.optionIndex === oIndex) ||
                                            question.correctAnswer === oIndex.toString();
                      const correctAnswerData = question.correctAnswers?.find(ca => ca.optionIndex === oIndex);

                      return (
                        <div
                          key={oIndex}
                          className={`review-option ${isUserSelected ? 'selected' : ''} ${isCorrectAnswer ? 'correct-answer' : ''}`}
                        >
                          <span className="option-text">{option}</span>
                          {isUserSelected && !isCorrectAnswer && (
                            <span className="wrong-indicator">✗ Incorrect</span>
                          )}
                          {isCorrectAnswer && (
                            <span className="correct-indicator">
                              ✓ Correct {correctAnswerData ? `(${correctAnswerData.weight}%)` : ''}
                            </span>
                          )}
                          {isUserSelected && isCorrectAnswer && (
                            <span className="user-correct">Your Answer ✓</span>
                          )}
                        </div>
                      );
                    })}
                  </div>
                  {!isCorrect && (
                    <div className="correct-answer-note">
                      <p>Correct answer: {question.questionType === 'multiple'
                        ? (question.correctAnswers && question.correctAnswers.length > 0
                            ? question.correctAnswers.map(ca => question.options[ca.optionIndex]).join(', ')
                            : 'N/A')
                        : (question.options[parseInt(question.correctAnswer)] ?? 'N/A')
                      }</p>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
          <div className="quiz-actions">
            <button className="quiz-retry-btn" onClick={handleRetry}>
              Try Again
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
export default QuizSubmission;