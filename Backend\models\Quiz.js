const mongoose = require('mongoose');

const quizSchema = new mongoose.Schema({
  title: { type: String, required: true },
  description: { type: String },
  course: { type: mongoose.Schema.Types.ObjectId, ref: 'Course', required: true },
  questions: [{
    question: { type: String, required: true },
    options: [{ type: String, required: true }],
    // Support for multiple correct answers with weights
    correctAnswers: [{
      optionIndex: { type: Number, required: true },
      weight: { type: Number, required: true, min: 0, max: 100 } // percentage weight
    }],
    // Keep legacy field for backward compatibility
    correctAnswer: { type: String },
    explanation: { type: String },
    points: { type: Number, default: 1 },
    // Question type: 'single' for single correct answer, 'multiple' for multiple correct answers
    questionType: { type: String, enum: ['single', 'multiple'], default: 'single' }
  }],
  timeLimit: { type: Number }, // in minutes
  passingScore: { type: Number, default: 60 }, // percentage - can be customized per quiz
  attempts: [{
    user: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    score: { type: Number },
    answers: [{
      questionIndex: { type: Number },
      selectedAnswer: [{ type: String }], // Array of selected answers for both single and multiple answer questions
      isCorrect: { type: Boolean },
      partialScore: { type: Number, default: 0 } // Partial credit score
    }],
    completedAt: { type: Date, default: Date.now }
  }]
}, { timestamps: true });

module.exports = mongoose.model('Quiz', quizSchema);