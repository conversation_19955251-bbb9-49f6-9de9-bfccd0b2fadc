const Quiz = require('../models/Quiz');
const Course = require('../models/Course');
const AppError = require('../utils/appError');
const catchAsync = require('../utils/catchAsync');

// Create a new quiz
exports.createQuiz = catchAsync(async (req, res, next) => {
  const { courseId } = req.params;
  const course = await Course.findById(courseId);

  if (!course) {
    return next(new AppError('Course not found', 404));
  }

  if (course.creator.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
    return next(new AppError('Not authorized to create quiz for this course', 403));
  }

  const quiz = await Quiz.create({
    ...req.body,
    course: courseId
  });

  course.quizzes.push(quiz._id);
  await course.save();

  res.status(201).json({
    status: 'success',
    data: quiz
  });
});

// Get a quiz
exports.getQuiz = catchAsync(async (req, res, next) => {
  const quiz = await Quiz.findById(req.params.quizId)
    .populate('course', 'title');

  if (!quiz) {
    return next(new AppError('Quiz not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: quiz
  });
});

// Update a quiz
exports.updateQuiz = catchAsync(async (req, res, next) => {
  const quiz = await Quiz.findById(req.params.quizId);

  if (!quiz) {
    return next(new AppError('Quiz not found', 404));
  }

  const course = await Course.findById(quiz.course);
  if (course.creator.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
    return next(new AppError('Not authorized to update this quiz', 403));
  }

  const updatedQuiz = await Quiz.findByIdAndUpdate(
    req.params.quizId,
    req.body,
    { new: true, runValidators: true }
  );

  res.status(200).json({
    status: 'success',
    data: updatedQuiz
  });
});

// Delete a quiz
exports.deleteQuiz = catchAsync(async (req, res, next) => {
  const quiz = await Quiz.findById(req.params.quizId);

  if (!quiz) {
    return next(new AppError('Quiz not found', 404));
  }

  const course = await Course.findById(quiz.course);
  if (course.creator.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
    return next(new AppError('Not authorized to delete this quiz', 403));
  }

  await Quiz.findByIdAndDelete(req.params.quizId);

  // Remove quiz reference from course
  course.quizzes = course.quizzes.filter(q => q.toString() !== quiz._id.toString());
  await course.save();

  res.status(204).json({
    status: 'success',
    data: null
  });
});

// Submit quiz attempt
exports.submitQuiz = catchAsync(async (req, res, next) => {
  const quiz = await Quiz.findById(req.params.quizId);

  if (!quiz) {
    return next(new AppError('Quiz not found', 404));
  }

  const { answers } = req.body;
  let score = 0;
  const totalPoints = quiz.questions.reduce((acc, q) => acc + (q.points || 1), 0);

  // Handle different answer formats (array or object with question IDs as keys)
  let evaluatedAnswers = [];

  // Helper function to calculate partial score for a question
  const calculatePartialScore = (question, selectedAnswers) => {
    if (question.questionType === 'multiple' && question.correctAnswers && question.correctAnswers.length > 0) {
      // Multiple correct answers with weights
      let partialScore = 0;
      const selectedIndices = Array.isArray(selectedAnswers)
        ? selectedAnswers.map(ans => parseInt(ans))
        : [parseInt(selectedAnswers)];

      // Calculate score based on correct selections and their weights
      question.correctAnswers.forEach(correctAnswer => {
        if (selectedIndices.includes(correctAnswer.optionIndex)) {
          partialScore += correctAnswer.weight;
        }
      });

      // Penalize for incorrect selections (optional - can be configured)
      const incorrectSelections = selectedIndices.filter(index =>
        !question.correctAnswers.some(ca => ca.optionIndex === index)
      );

      // Reduce score for incorrect selections (10% penalty per incorrect selection)
      partialScore = Math.max(0, partialScore - (incorrectSelections.length * 10));

      return Math.min(100, partialScore) / 100; // Convert to decimal (0-1)
    } else {
      // Single correct answer (legacy support)
      const selectedAnswer = Array.isArray(selectedAnswers) ? selectedAnswers[0] : selectedAnswers;
      const isCorrect = selectedAnswer === question.correctAnswer ||
                       selectedAnswer === question.correctAnswer?.toString();
      return isCorrect ? 1 : 0;
    }
  };

  if (Array.isArray(answers)) {
    // Handle array format (legacy)
    evaluatedAnswers = answers.map((answer, index) => {
      const question = quiz.questions[index];
      const partialScore = calculatePartialScore(question, answer);
      const isCorrect = partialScore >= 1; // Full credit

      score += (question.points || 1) * partialScore;

      return {
        questionIndex: index,
        questionId: question._id,
        selectedAnswer: answer,
        selectedAnswers: Array.isArray(answer) ? answer : [answer],
        correctAnswer: question.correctAnswer,
        correctAnswers: question.correctAnswers,
        isCorrect,
        partialScore: partialScore * (question.points || 1)
      };
    });
  } else {
    // Handle object format with question IDs as keys
    evaluatedAnswers = quiz.questions.map((question, index) => {
      const questionId = question._id.toString();
      const selectedAnswer = answers[questionId];
      const partialScore = calculatePartialScore(question, selectedAnswer);
      const isCorrect = partialScore >= 1; // Full credit

      score += (question.points || 1) * partialScore;

      return {
        questionIndex: index,
        questionId: questionId,
        selectedAnswer: selectedAnswer,
        selectedAnswers: Array.isArray(selectedAnswer) ? selectedAnswer : [selectedAnswer],
        correctAnswer: question.correctAnswer,
        correctAnswers: question.correctAnswers,
        isCorrect,
        partialScore: partialScore * (question.points || 1)
      };
    });
  }

  const finalScore = Math.round((score / totalPoints) * 100);
  const passed = finalScore >= quiz.passingScore;

  // Save the attempt
  quiz.attempts.push({
    user: req.user._id,
    score: finalScore,
    answers: evaluatedAnswers,
    completedAt: new Date()
  });

  await quiz.save();

  // Return detailed results including correct answers
  res.status(200).json({
    status: 'success',
    data: {
      score,
      totalPoints,
      percentage: finalScore,
      passed,
      answers: evaluatedAnswers,
      questions: quiz.questions.map(q => ({
        id: q._id,
        question: q.question,
        options: q.options,
        correctAnswer: q.correctAnswer,
        points: q.points || 1
      }))
    }
  });
});

// Get quiz results
exports.getQuizResults = catchAsync(async (req, res, next) => {
  const quiz = await Quiz.findById(req.params.quizId)
    .populate('attempts.user', 'name email');

  if (!quiz) {
    return next(new AppError('Quiz not found', 404));
  }

  const userAttempt = quiz.attempts.find(
    attempt => attempt.user._id.toString() === req.user._id.toString()
  );

  if (!userAttempt) {
    return next(new AppError('No attempt found for this quiz', 404));
  }

  res.status(200).json({
    status: 'success',
    data: userAttempt
  });
});